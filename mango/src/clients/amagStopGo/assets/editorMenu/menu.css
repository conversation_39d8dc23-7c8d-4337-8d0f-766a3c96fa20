@import '../variables.css';
@import '../buttons.css';
@import '../../assets/images.css';
@import '../../modules/buttons/Button';

:root {
	--editor-menu-container-poistion-left: 0;
	--editor-menu-container-poistion-left-mobile: 0;
	--editor-menu-container-position-top: 88px;
	--editor-menu-container-position-top-mobile: 62px;
	--editor-menu-container-background: var(--dark-gray);
	--editor-menu-container-padding: 1.25em;
	--editor-menu-container-clip-mask: rect(auto, auto, 60px, auto);
	--editor-menu-container-clip-mask-open: rect(auto, auto, 100vh, auto);

	--editor-menu-border: 1px solid #ddd;
	--editor-menu-position-top: 40px;
	--editor-menu-position-right: 25px;
	--editor-menu-position-right-mobile: 20px;

	--editor-menu-item-last-child-border: var(--editor-menu-border);
	--editor-menu-item-padding-top: 15px;
	--editor-menu-item-margin-top: 10px;

	--editor-menu-font-thin: var(--font-text);
	--editor-menu-font-light: var(--font-text);
	--editor-menu-font-regular: var(--font-text);
	--editor-menu-font-medium: var(--font-head);
	--editor-menu-font-bold: var(--font-head);

	--editor-menu-caption-font-size: var(--default-font-size);
	--editor-menu-caption-line-height: var(--default-line-height);

	--editor-menu-small-font-size: var(--small-font-size);
	--editor-menu-small-line-height: var(--small-line-height);

	--editor-menu-background-color: var(--editor-menu-container-background);
	--editor-menu-background-color-hover: var(--primary-color);

	--editor-menu-button-background-image: var(--iconEditorMenu);
	--editor-menu-button-border: solid 1px color(var(--white) alpha(-90%));
	--editor-menu-button-background-size: 16px;
	--editor-menu-button-padding: 4px;
	--editor-menu-button-position-right: 25px;
	--editor-menu-button-position-right-mobile: 1.25em;
	--editor-menu-button-arrow-position-right: 17px;
	--editor-menu-button-arrow-position-right-mobile: 17px;
	--editor-menu-button-arrow-translate-y: -10px;
	--editor-menu-button-arrow-line-height: 25px;
	--editor-menu-dialog-position-right: 20px;
	--editor-menu-dialog-position-right-mobile: 20px;

	--editor-menu-button-width: 32px;
	--editor-menu-button-height: var(--editor-menu-button-width);

	--editor-menu-button-text-margin-right: 45px;
	--editor-menu-button-text-vertical-align: middle;

	--editor-menu-button-back-background-image: var(--iconEditorMenuBack);

	--editor-menu-item-color: var(--dark-gray);
	--editor-menu-item-color-hover: var(--primary-color);

	--editor-menu-buttons: {
		@apply --button;
	};

	--editor-menu-container: {
		clear: both;

		@media (--screen-middle-max) {
			top: 145px;
		}

		@media (--screen-tablet) {
			top: 62px;
		}
	};

	--editor-menu-dot-container: {
		clear: both;
	};

	--editor-menu-publish-button-containers: {
		clear: both;
	};
}

/* @flow */
import React from 'react';
import moment from 'moment';
import { connect } from 'react-redux';
import s from 'react-datepicker/dist/react-datepicker-cssmodules.css';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import classNames from 'classnames';
import styles from './DateTimePicker.css';
import DatePicker from './DatePicker';
import { getLocale } from '../i18n/selectors';
import combineHOCs from '../../tango/hoc/combineHOCs';

type Props = {
	value: any,
	onChange: Function,
	placeholderText?: string,
	minDate?: any,
	maxDate?: any,
	disabled?: boolean,
	minutesStep?: number,
	dateFormat?: string,
	timeFormat?: string,
	className?: string,
	containerClassName?: string,
	calendarClassName?: string,
	popperPlacement?: | "auto"
	| "auto-left"
	| "auto-right"
	| "bottom"
	| "bottom-end"
	| "bottom-start"
	| "left"
	| "left-end"
	| "left-start"
	| "right"
	| "right-end"
	| "right-start"
	| "top"
	| "top-end"
	| "top-start",
	fixedHeight?: boolean
};

type ConnectedProps = {
	locale?: string
};

type State = {
	inputValue: string,
};

class DateTimePicker extends React.PureComponent {
	props: Props & ConnectedProps;

	static getDerivedStateFromProps(props: Props & ConnectedProps, state: State) {
		const { inputValue } = state;
		const {
			value,
			locale,
			dateFormat = 'DD.MM.YYYY',
			timeFormat = 'HH:mm',
		} = props;

		if (value) {
			moment.locale(locale);
			const dateTimeFormat = [dateFormat, timeFormat].join(' ');
			const nextInputValue = value.format(dateTimeFormat);

			if (inputValue !== nextInputValue) {
				return {
					inputValue: nextInputValue,
				};
			}
		}

		return null;
	}

	state: State = { inputValue: '' };

	handleChangeRaw = (event) => {
		this.setState({ inputValue: event.target.value });
	};

	handleKeyPress = (event) => {
		if (event.key === 'Enter') {
			this.modifyRawInputValue();
		}
	};

	modifyRawInputValue = () => {
		const {
			onChange,
			dateFormat = 'DD.MM.YYYY',
			timeFormat = 'HH:mm',
			locale,
		} = this.props;

		moment.locale(locale);

		const { inputValue } = this.state;
		const dateTimeFormat = [dateFormat, timeFormat].join(' ');
		const modifiedRawValue = moment(inputValue, dateTimeFormat).format(
			dateTimeFormat,
		);

		if (modifiedRawValue !== 'Invalid date') {
			onChange(moment(modifiedRawValue, dateTimeFormat));
		}

		this.setState({ inputValue: modifiedRawValue });
	};

	handleChange = (newValue) => {
		const {
			onChange,
			dateFormat = 'DD.MM.YYYY',
			timeFormat = 'HH:mm',
			locale,
		} = this.props;

		moment.locale(locale);

		const dateTimeFormat = [dateFormat, timeFormat].join(' ');

		if (newValue) {
			this.setState({ inputValue: newValue.format(dateTimeFormat) });
		}

		onChange(newValue);
	};

	render() {
		const {
			value,
			placeholderText,
			minDate,
			maxDate,
			disabled,
			minutesStep,
			dateFormat = 'LLL',
			timeFormat = 'HH:mm',
			className,
			containerClassName,
			calendarClassName,
			popperPlacement,
			fixedHeight,
		} = this.props;
		const { inputValue } = this.state;

		return (
			<DatePicker
				value={value || null}
				rawValue={inputValue || ''}
				onChange={this.handleChange}
				placeholderText={placeholderText}
				minDate={minDate}
				maxDate={maxDate}
				disabled={disabled}
				showTimeSelect
				dateFormat={dateFormat}
				timeIntervals={minutesStep}
				timeFormat={timeFormat}
				className={className}
				containerClassName={containerClassName}
				calendarClassName={classNames(calendarClassName, styles.dateTimePicker)}
				popperPlacement={popperPlacement}
				fixedHeight={fixedHeight}
				onChangeRaw={this.handleChangeRaw}
				onKeyDown={this.handleKeyPress}
				onBlur={this.modifyRawInputValue}
			/>
		);
	}
}

function mapStateToProps(state) {
	return {
		locale: getLocale(state),
	};
}

export default (combineHOCs([connect(mapStateToProps), withStyles(styles, s)])(
	DateTimePicker,
): ReactComponent<Props>);
